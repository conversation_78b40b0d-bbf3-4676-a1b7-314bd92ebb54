import 'dart:convert';
import 'dart:developer';

import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_detail_page.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_dialog.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/application_util.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:location/location.dart' as gps;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/model/repair_service_request.dart';
import '../../../logger/logger.dart';

class DigitalLopaPage extends StatefulWidget {
  static const routeName = 'digital-lopa';
  final bool isUpdate;
  final bool isDirecRepair;
  final bool isNotification;

  const DigitalLopaPage({Key? key, this.isUpdate = false, required this.isDirecRepair, required this.isNotification}) : super(key: key);

  @override
  _DigitalLopaPageState createState() => _DigitalLopaPageState();
}

class _DigitalLopaPageState extends State<DigitalLopaPage> {
  var selectedObject;
  var aisleGroup;
  int seatInRow = 0;
  bool isMethodCalled = false;
  gps.LocationData? locationData;
  List<RepairServiceRequest> serviceRequestList = [];
  late ScrollController _scrollController;

  ///FLAT TO CHECK IF REQUEST IS SENT
  bool isDirectRepairSaved = false;
  bool isServiceRequestSaved = false;

  ///CHECK FOR MODE
  bool isDirectRepairMode = false;
  bool isServiceRequestMode = false;

  @override
  void initState() {
    _initLocation();
    _scrollController = ScrollController();
    String seatTag = ApplicationUtil.getEquipmentType();
    if (widget.isDirecRepair) {
      if (SelectedFleetCubit.selectedSeat != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _clearSeatSelection();
          SelectedFleetCubit.selectedSeat!.seatSelected = true;
          showDialog(
              context: context,
              builder: (BuildContext context) {
                return LopaServiceRequestDialog(
                  title: widget.isDirecRepair
                      ? "$seatTag: ${SelectedFleetCubit.selectedSeat!.name}"
                      : '${AppLocalizations.of(context)!.repair}$seatTag: ${SelectedFleetCubit.selectedSeat!.name} ',
                  seatItemList: SelectedFleetCubit.selectedSeat!.partList,
                  imageList: SelectedFleetCubit.selectedSeat!.imageList,
                  isDirectRepair: widget.isDirecRepair,
                  repairImageList: SelectedFleetCubit.selectedSeat!.repairList,
                  equipmentName: SelectedFleetCubit.selectedSeat!.name,
                  seatDetail: SelectedFleetCubit.selectedSeat!,
                );
              }).then((value) {
            setState(() {});
          });
        });
      }
    }
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: _getBody(),
          ),
        ),
      ),
      floatingActionButton: !widget.isUpdate
          ? FloatingActionButton(
              onPressed: () {
                var isEmpty = false;
                /*SelectedFleetCubit.seatCategoryList.where((category) => category.seatDetailList==()).isEmpty;
                log(isEmpty.toString());*/
                if (isEmpty) {
                  ApplicationUtil.showUnsavedWarningAlertDialog(context);
                } else {
                  Navigator.pop(context);
                }
              },
              child: Container(margin: const EdgeInsets.only(right: 5), child: const FaIcon(FontAwesomeIcons.chevronLeft)),
            )
          : Container(),
    );
  }

  _getLopaLayoutFromJson() {
    return FutureBuilder(
      future: _getAllServiceRequests(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            width: MediaQuery.of(context).size.width,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else {
          print("All Service requests length: ${serviceRequestList.length}");
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(150),
                  topRight: Radius.circular(150),
                ),
                color: Colors.white),
            child: Container(
              padding: const EdgeInsets.all(5),
              margin: const EdgeInsets.only(top: 100),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  _generateSeatsLayout(),
                  //_sheetCurtain(),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  _generateSeatsLayout() {
    /*SeatCategory firstClassSeatCategory = seatCategoryList
        .where((element) => element.className == 'First Class')
        .first;*/
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: SelectedFleetCubit.seatCategoryList.length,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black),
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(border: Border.all(color: Colors.black)),
                child: Text(
                  SelectedFleetCubit.seatCategoryList[index].className,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
              ),
              _generateRowAndColumnForSeats(SelectedFleetCubit.seatCategoryList[index].seatDetailList,
                  SelectedFleetCubit.seatCategoryList[index].rowCount, SelectedFleetCubit.seatCategoryList[index].className),
            ],
          ),
        );
      },
    );
  }

  _singleSeat(
    SeatDetail seatDetail,
    List<SeatDetail> seatDetailList,
    String seatLocationId, RepairServiceRequest? auditEquipment,
  ) {
    bool isSelected = seatDetail.partList.where((element) => element.isChecked == true).toList().isNotEmpty;
    return InkWell(
      onTap: () {
        for (var element in SelectedFleetCubit.seatCategoryList) {
          for (var element in element.seatDetailList) {
            element.seatSelected = false;
          }
        }
        seatDetail.seatSelected = true;
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return LopaServiceRequestDialog(
                  title: !widget.isDirecRepair
                      ? "${AppLocalizations.of(context)!.seat}: ${seatDetail.name}"
                      : '${AppLocalizations.of(context)!.repairSeat}: ${seatDetail.name} ',
                  seatItemList: seatDetail.partList,
                  imageList: seatDetail.imageList,
                  isDirectRepair: widget.isDirecRepair,
                  repairImageList: seatDetail.repairList,
                  equipmentName: seatDetail.name,
                  seatDetail: seatDetail);
            }).then((value) {
          setState(() {});
        });
        //setState(() {});
      },
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        elevation: 5,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Center(
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Opacity(
                    opacity: 0.7,
                    child: Container(
                      height: 55,
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(15),
                            topLeft: Radius.circular(15),
                          ),
                          color: _getForeGroundColorCode(isSelected, seatDetail,auditEquipment)),
                    ),
                  ),
                  Container(
                    height: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                          //bottomLeft: Radius.circular(5),//optional
                          //bottomRight: Radius.circular(5),//optional
                        ),
                        color: _getBackgroundGroundColorCode(isSelected, seatDetail,auditEquipment)),
                    child: Center(
                      child: Text(
                        seatDetail.name,
                        style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _getBottomOption(List<SeatCategory> seatCategoryList) {
    if (!widget.isUpdate) {
      if (widget.isDirecRepair) {
        return BlocListener<ServiceRequestBloc, ServiceRequestState>(
          listener: (context, state) {
            if (state is ServiceRequestSaved) {
              isDirectRepairSaved = true;
              if (isDirectRepairMode == true && isServiceRequestMode == true) {
                if (isServiceRequestSaved == true && isDirectRepairSaved == true) {
                  serviceRequestBloc.add(SendPendingServiceRequestToServer());

                  Navigator.pop(context, false);
                }
              } else {
                serviceRequestBloc.add(SendPendingServiceRequestToServer());

                Navigator.pop(context, false);
              }
            }
          },
          child: BlocListener<RepairBloc, RepairState>(
            listener: (context, state) {
              if (state is RepairError) {
                ApplicationUtil.showSnackBar(context: context, message: state.error);
              }
              if (state is RepairSaved) {
                isServiceRequestSaved = true;
                if (isDirectRepairMode == true && isServiceRequestMode == true) {
                  if (isServiceRequestSaved == true && isDirectRepairSaved == true) {
                    serviceRequestBloc.add(SendPendingServiceRequestToServer());

                    Navigator.pop(context, false);
                  }
                } else {
                  repairBloc.add(SendPendingRepairToServer());
                  Navigator.pop(context, false);
                }
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: ElevatedButton(
                  style: ButtonStyle(
                    padding: MaterialStateProperty.all(
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 70),
                    ),
                  ),
                  onPressed: () {
                    if (locationData == null){
                      ApplicationUtil.showWarningAlertDialog(
                        context,
                        title: AppLocalizations.of(context)!.locationPermissionRequired,
                        desc:  kIsWeb ?  AppLocalizations.of(context)!.appRequiresLocationPermissionforweb: AppLocalizations.of(context)!.appRequiresLocationPermission,
                        negativeLabel: AppLocalizations.of(context)!.okay,
                      );
                    }else{
                      int? userId = getIt<SharedPreferences>().getInt('userId');
                      Map<String, List<Map<String, dynamic>>> directRepairMapData = {};
                      Map<String, List<Map<String, dynamic>>> imageData = {};
                      Map<String, List<Map<String, dynamic>>> repairImageData = {};

                      ///FOR SERVICE REQUEST
                      Map<String, List<Map<String, dynamic>>> serviceRequestMapData = {};
                      for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
                        // LOOP EACH SEAT
                        for (var seatDetail in seatCategory.seatDetailList) {
                          List<Map<String, dynamic>> checkPartList = [];
                          // LOOP EACH PART
                          for (var part in seatDetail.partList) {
                            Map<String, dynamic> partsMap = {};
                            if (part.isChecked) {
                              partsMap['PartId'] = part.part1;
                              partsMap['Completed'] = seatDetail.markSeatIsRepaired;
                              partsMap['LocationId'] = part.equipmentLocationId;
                              checkPartList.add(partsMap);
                            }
                          }
                          if (checkPartList.isNotEmpty) {
                            if (seatDetail.markSeatIsRepaired) {
                              directRepairMapData[seatDetail.name] = checkPartList;
                              imageData[seatDetail.name] = seatDetail.imageList;
                              repairImageData[seatDetail.name] = seatDetail.repairList;
                            } else {
                              serviceRequestMapData[seatDetail.name] = checkPartList;
                              imageData[seatDetail.name] = seatDetail.imageList;
                            }
                          }
                        }
                      }
                      if (directRepairMapData.isEmpty && serviceRequestMapData.isEmpty) {
                        ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.selectAtLeastOnePartContinue);
                      }
                      if (directRepairMapData.isNotEmpty) {
                        isDirectRepairMode = true;
                        _saveDirectRepairServiceRequestInDatabase(directRepairMapData, userId, imageData, repairImageData);
                      }
                      if (serviceRequestMapData.isNotEmpty) {
                        isServiceRequestMode = true;

                        _saveNewServiceRequestInDatabase(serviceRequestMapData, userId, imageData);
                      }
                      /* log(json.encode(directRepairMapData));
                    log('dataaaa');
                    log(json.encode(serviceRequestMapData));*/
                      /* Navigator.pushNamed(
                  context,
                  LopaServiceRequestPage.routeName,
                  arguments: LopaServiceRequestParameter(
                      fleetLocation: SelectedFleetCubit.fleetLocation,
                      seatCategoryList: seatCategoryList,
                      isDirectRepair: widget.isDirecRepair,
                      isNotification: widget.isNotification),
                ).then((value) {
                  //if thi is returning on press of back then go to previous page
                  if (value == null) {
                    if (widget.isDirecRepair == true ||
                        widget.isNotification == true) {
                      Navigator.pop(context);
                    }
                  }
                });*/
                    }
                  },
                  child: Text(
                    AppLocalizations.of(context)!.submit,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ),
              ),
            ),
          ),
        );
      }
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
        child: Align(
          alignment: Alignment.bottomCenter,
          child: ElevatedButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(vertical: 12, horizontal: 70),
              ),
            ),
            onPressed: () {
              Map<String, List<Map<String, dynamic>>> mapData = {};
              Map<String, List<Map<String, dynamic>>> imageData = {};
              Map<String, List<Map<String, dynamic>>> serviceRequestImageData = {};
              for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
                // LOOP EACH SEAT
                for (var seatDetail in seatCategory.seatDetailList) {
                  List<Map<String, dynamic>> checkPartList = [];
                  // LOOP EACH PART
                  for (var part in seatDetail.partList) {
                    Map<String, dynamic> partsMap = {};
                    if (part.isChecked) {
                      partsMap['PartId'] = part.part1;
                      partsMap['Completed'] = true;
                      partsMap['LocationId'] = part.equipmentLocationId;
                      checkPartList.add(partsMap);
                    }
                  }
                  if (checkPartList.isNotEmpty) {
                    mapData[seatDetail.name] = checkPartList;
                    imageData[seatDetail.name] = seatDetail.imageList;
                    serviceRequestImageData[seatDetail.name] = seatDetail.repairList;
                  }
                }
              }
              if (mapData.isNotEmpty) {
                Navigator.pushNamed(
                  context,
                  LopaServiceRequestPage.routeName,
                  arguments: LopaServiceRequestParameter(
                      fleetLocation: SelectedFleetCubit.fleetLocation,
                      seatCategoryList: seatCategoryList,
                      isDirectRepair: widget.isDirecRepair,
                      isNotification: widget.isNotification),
                ).then((value) {
                  //if thi is returning on press of back then go to previous page
                  if (value == null) {
                    if (widget.isDirecRepair == true || widget.isNotification == true) {
                      Navigator.pop(context);
                    }
                  }
                });
              } else {
                ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.selectAtLeastOnePartContinue);
              }
            },
            child: Text(
              AppLocalizations.of(context)!.continueString,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ),
      );
    }
    return Align(
      alignment: Alignment.bottomCenter,
      child: ElevatedButton(
        style: ButtonStyle(
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(vertical: 12, horizontal: 70),
          ),
        ),
        onPressed: () => Navigator.pop(context),
        child: Text(
          AppLocalizations.of(context)!.done,
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
    );
  }

  _generateRowAndColumnForSeats(
    List<SeatDetail> firstClassSeatList,
    int rowCount,
    String className,
  ) {
    int rowSeatCount = rowCount;
    // int columnCount = firstClassSeatList.length/rowSeatCount;
    List<Widget> rowList = [];
    for (int seatPosition = 0; seatPosition < firstClassSeatList.length;) {
      List<Widget> rowWidgets = [];
      List<SeatDetail> rowSeatList = [];
      for (int index = seatPosition; index < firstClassSeatList.length && index < seatPosition + firstClassSeatList[index].rowCount!;index++) {
        SeatDetail seatDetail = firstClassSeatList[index];
        rowSeatCount=firstClassSeatList[index].rowCount!;
        int seatRowNumber = seatPosition ~/ rowSeatCount + 1;
        String seatLocationId = _getSeatLocation(className, seatRowNumber);
        bool isEmpty = serviceRequestList.where((element) => element.equipmentName == seatDetail.name).isEmpty;
        RepairServiceRequest? auditEquipment;
        if (!isEmpty) {
          auditEquipment = serviceRequestList.where((element) => seatDetail.name == element.equipmentName).first;
        }
        /*serviceRequestList[0].partsMap!.forEach((key, value) {
          for (var data in List<Map<String, dynamic>>.from(value)) {
            //print(element['PartId']);
            if (seatDetail.partList.where((element) => element.part1 == data['PartId']).isNotEmpty) {
              seatDetail.partList.where((element) => element.part1 == data['PartId']).first.hasServiceRequest = true;
            }
          }
        });*/
        serviceRequestList.forEach((srElement) {
          srElement.partsMap!.forEach((key, value) {
            for (var data in List<Map<String, dynamic>>.from(value)) {
              //print(element['PartId']);
              if(seatDetail.name == srElement.equipmentName){
                if (seatDetail.partList.where((element) => element.part1 == data['PartId']).isNotEmpty) {
                  seatDetail.partList.where((element) => element.part1 == data['PartId']).first.hasServiceRequest = true;
                }
              }
            }
          });
          /*if(srElement.equipmentName == seatDetail.name){
            seatDetail.partList.forEach((seatDetailPartElement) {
              var inCompletePart = srElement.partsMap.where((element) => element.completed == false).length;
              seatDetailPartElement
            });
          }*/
        });
        rowWidgets.add(Stack(
          alignment: Alignment.topRight,
          children: [
            Container(
              margin: const EdgeInsets.all(5),
              width: 75,
              child: _singleSeat(seatDetail, firstClassSeatList, seatLocationId,auditEquipment),
            ),
            _getSelectedPartBadge(seatDetail,auditEquipment),
          ],
        ));
        rowSeatList.add(seatDetail);
      }
      seatPosition = seatPosition + rowSeatCount;
      //Set all row selection to false only one time

      rowList.add(
        InkWell(
          onTap: () => null,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            decoration: BoxDecoration(
              border: Border.all(width: 2, color: Colors.grey),
            ),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(5),
              child: Center(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: rowWidgets),
                ),
              ),
            ),
          ),
        ),
      );
    }

    isMethodCalled = true;
    return Column(
      children: rowList,
    );
  }

  _getSelectedPartBadge(SeatDetail seatDetail, RepairServiceRequest? auditEquipment) {
    int srCount = seatDetail.partList.where((element) => (element.hasServiceRequest == true)).toList().length;
    int newSRCount = seatDetail.partList.where((element) => (element.isChecked == true)).toList().length;
    if (srCount > 0 && newSRCount > 0) {
      return Positioned(
        right: -6,
        top: -6,
        child: Container(
          margin: const EdgeInsets.all(7.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: Theme.of(context).primaryColor,
          ),
          constraints: const BoxConstraints(
            minWidth: 21,
            minHeight: 21,
          ),
          child: Center(
            child: Text(
              '${newSRCount + srCount}',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 13, color: Colors.white, height: 1.2),
            ),
          ),
        ),
      );
    }
    else if (srCount>0) {
      return Positioned(
        right: -6,
        top: -6,
        child: Container(
          margin: const EdgeInsets.all(7.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: Colors.grey,
          ),
          constraints: const BoxConstraints(
            minWidth: 21,
            minHeight: 21,
          ),
          child: Center(
            child: Text(
              '$srCount',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 13, color: Colors.white, height: 1.2),
            ),
          ),
        ),
      );
    }  else if (newSRCount>0) {
      return Positioned(
        right: -6,
        top: -6,
        child: Container(
          margin: const EdgeInsets.all(7.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: Theme.of(context).primaryColor,
          ),
          constraints: const BoxConstraints(
            minWidth: 21,
            minHeight: 21,
          ),
          child: Center(
            child: Text(
              '$newSRCount',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 13, color: Colors.white, height: 1.2),
            ),
          ),
        ),
      );
    } else{
      return Container();
    }
  }

  _getBody() {
    if (SelectedFleetCubit.seatCategoryList.isNotEmpty) {
      return Stack(
        alignment: Alignment.topCenter,
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height),
            child: Scrollbar(
              controller: _scrollController,
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /*SizedBox(
                      height: MediaQuery.of(context).padding.top + 10,
                    ),*/
                    _getFleetAndSubFleetName(),
                    const SizedBox(
                      height: 5,
                    ),
                    _topAppBar(),
                    const SizedBox(
                      height: 5,
                    ),
                    _getLopaLayoutFromJson()
                  ],
                ),
              ),
            ),
          ),
          widget.isUpdate ? _getTopMenuOption() : Container(),
          _getBottomOption(SelectedFleetCubit.seatCategoryList)
        ],
      );
    } else {
      return Text(AppLocalizations.of(context)!.noRecordFound);
    }
  }

  _getSubFleetName() {
    if (SelectedFleetCubit.fleetLocation.subFleetName != AppLocalizations.of(context)!.select) {
      return Row(
        children: [
          Text(
            SelectedFleetCubit.fleetLocation.subFleetName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            child: const FaIcon(
              FontAwesomeIcons.angleRight,
              size: 20,
            ),
          ),
        ],
      );
    }
    return Container();
  }

  _getTopMenuOption() {
    return AnimatedContainer(
      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top - 10),
      height: 70,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(bottomRight: Radius.circular(30), bottomLeft: Radius.circular(30)),
      ),
      duration: const Duration(milliseconds: 400),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10),
              child: Text(
                AppLocalizations.of(context)!.updateParts,
                style: const TextStyle(color: Colors.white, fontSize: 20, height: 1.2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getLocationId() {
    FleetLocation fleetLocation = SelectedFleetCubit.fleetLocation;
    String locationId = fleetLocation.fleetName;
    if (fleetLocation.subFleetName != 'Select') {
      if (fleetLocation.subFleetName.contains('-')) {
        locationId += '-' + fleetLocation.subFleetName.replaceAll('-', '_');
      } else {
        locationId += '-' + fleetLocation.subFleetName;
      }
    }
    locationId += '-' + fleetLocation.tailName;
    return locationId;
  }

  _getFleetAndSubFleetName() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          Text(
            SelectedFleetCubit.fleetLocation.fleetName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            child: const FaIcon(
              FontAwesomeIcons.angleRight,
              size: 20,
            ),
          ),
          _getSubFleetName(),
          Text(
            SelectedFleetCubit.fleetLocation.tailName,
            style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 18, height: 1.2),
          ),
        ],
      ),
    );
  }

  _getForeGroundColorCode(bool isSelected, SeatDetail seatDetail, RepairServiceRequest? auditEquipment) {
    Color color = Colors.blue.shade200;
    int count = seatDetail.partList.where((element) => (element.hasServiceRequest == true)).toList().length;
    if (auditEquipment != null) {
      bool hasServiceRequest = serviceRequestList.where((element) => element.equipmentName == auditEquipment.equipmentName).isNotEmpty;
      if (hasServiceRequest && count ==2) {
        color = Colors.blueGrey.shade200;
      } else {
        color = Colors.orange.shade200;
      }
    }
    return color;
  }

  _getBackgroundGroundColorCode(bool isSelected, SeatDetail seatDetail, RepairServiceRequest? auditEquipment) {
    Color color = Colors.blue;
    int count = seatDetail.partList.where((element) => (element.hasServiceRequest == true)).toList().length;

    if (auditEquipment != null) {
      ///CHECK IF EQUIPMENT HAS ALREADY SERVICE REQUEST
      bool hasServiceRequest = serviceRequestList.where((element) => element.equipmentName == auditEquipment.equipmentName).isNotEmpty;
      if (hasServiceRequest && count == 2 ) {
        ///
        RepairServiceRequest repairServiceRequest = serviceRequestList.where((element) => element.equipmentName == auditEquipment.equipmentName).first;
        repairServiceRequest.partsMap!.forEach((key, value) {
          /*for (var data in List<Map<String, dynamic>>.from(value)) {
            //print(element['PartId']);
            *//*if (auditEquipment.partsMap.where((element) => element.part1 == data['PartId']).isNotEmpty) {
              auditEquipment.partsMap.where((element) => element.part1 == data['PartId']).first.hasServiceRequest = true;
            }*//*
          }*/
        });
        color = Colors.grey;
      } else if (hasServiceRequest && count == 1){
        color=Colors.orange;
      }else {
        color = Colors.blue;
      }
    }
    return color;

    /*if (isSelected) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }*/
  }

  _topAppBar() {
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15),
      child: Text(
        widget.isDirecRepair ? customerId == 15 ? AppLocalizations.of(context)!.selectAssetToRepair: AppLocalizations.of(context)!.selectEquipmentToRepair : customerId ==15 ? AppLocalizations.of(context)!.selectAsset: AppLocalizations.of(context)!.selectEquipment,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black),
      ),
    );
  }

  void _clearSeatSelection() {
    for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
      for (var seatDetail in seatCategory.seatDetailList) {
        seatDetail.seatSelected = false;
        for (var part in seatDetail.partList) {
          part.isChecked = false;
        }
      }
    }
  }

  void _saveDirectRepairServiceRequestInDatabase(Map<String, List<Map<String, dynamic>>> mapData, int? userId,
      Map<String, List<Map<String, dynamic>>> imageData, Map<String, List<Map<String, dynamic>>> repairImageData) {
    repairBloc.add(
      SaveRepairRequest(
        repairCompanion: RepairCompanion(
            createdAt: moor.Value(DateTime.now()),
            createdBy: moor.Value(userId),
            currentLocationId: moor.Value(_getLocationId()),
            newLocationId: const moor.Value(null),
            parts: moor.Value(json.encode(mapData)),
            //extn: moor.Value(json.encode(extensionMap)),
            repairDocument: moor.Value(json.encode(repairImageData)),
            serviceRequestDocument: moor.Value(json.encode(imageData)),
            customerId: const moor.Value(null),
            equipmentId: const moor.Value(null),
            //requestId: moor.Value(startRepairBtnCubit.selectedRequestDetail!.requestId!),
            latitude: moor.Value(locationData?.latitude),
            longitude: moor.Value(locationData?.longitude)),
      ),
    );
  }

  String _getSeatLocation(String className, int seatRowNumber) {
    String seatClassCode = className.isNotEmpty ? className.trim().split(RegExp(' +')).map((s) => s[0].toLowerCase()).take(2).join() : '';
    return "${seatClassCode.toLowerCase()}-$seatRowNumber";
  }

  void _saveNewServiceRequestInDatabase(
      Map<String, List<Map<String, dynamic>>> serviceRequestMapData, int? userId, Map<String, List<Map<String, dynamic>>> imageData) {
    if (serviceRequestMapData.isNotEmpty) {
      serviceRequestBloc.add(
        SaveServiceRequest(
          serviceRequestCompanion: ServiceRequestCompanion(
            locationId: moor.Value(_getLocationId()),
            parts: moor.Value(json.encode(serviceRequestMapData)),
            newLocationId: const moor.Value(null),
            createdAt: moor.Value(DateTime.now()),
            equipmentBarcodeNumber: const moor.Value(null),
            equipmentId: const moor.Value(null),
            extn: const moor.Value(null),
            choiceId: const moor.Value(1),
            description: const moor.Value('No issue Description'),
            createdBy: moor.Value(userId),
            status: const moor.Value(0),
            customerId: const moor.Value(null),
            requestChoiceType: const moor.Value(null),
            requestType: const moor.Value('SERVICE_REQUEST'),
            equipmentName: const moor.Value(null),
            equipmentCategory: const moor.Value(null),
            document: moor.Value(json.encode(imageData)),
            latitude: moor.Value(locationData!.latitude),
            longitude: moor.Value(locationData?.longitude),
          ),
        ),
      );
    }
  }

  void _initLocation() async{
    var locationDetails = await ApplicationUtil.getGeoLocatorLocation();
    if (locationDetails != null && locationDetails.runtimeType != String) {
      locationData = locationDetails;
    } else if (locationDetails == "WEB") {
      Logger.i("Web platform detected in init - skipping location initialization");
    } else if(locationDetails == "TIMEOUT"){
      Logger.i("Timeout detected in init - skipping location initialization");
    }
  }

  _getAllServiceRequests() async {
    serviceRequestList = await ApiService().fetchLopaRepairServiceRequestList('&location=${_getLocationId()}&status=OPEN,'
        'PAUSE&requestType=NOTIFICATION,'
        'SERVICE_REQUEST&isRestricted=true');
    print(serviceRequestList.length);
    return serviceRequestList;
  }
}
