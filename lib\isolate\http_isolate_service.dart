import 'dart:convert';
import 'dart:developer';
import 'dart:isolate';

import 'package:alink/database/database.dart';
import 'package:alink/database/dbconfig/shared.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../logger/logger.dart';

import '../service_locator.dart';

class HttpIsolateService {
  Logger logger = Logger();

  sendServiceRequestThroughIsolate() async {
    ReceivePort receivePort = ReceivePort();
    //String token = await _getBasicAuth();
    Map<String, dynamic> map = getMapData();
    Logger.i('STARTING HTTP SERVICE REQUEST ISOLATE');
    if (!kIsWeb) {
      Isolate _isolate =
          await Isolate.spawn(checkServiceRequestAndSendToServer, map);
      Logger.i('ISOLATE HTTP SERVICE REQUEST ENDED');
      return await httpRequestRespond() as IsolateServiceResponse;
    } else {
      return checkServiceRequestAndSendToServer(map);
    }
  }
}

Future<dynamic> checkServiceRequestAndSendToServer(
    Map<String, dynamic> map) async {
  //Logger logger = Logger();
  //Logger.initialize(map['logPath'].toString() + '/logger.txt');
  //Logger.i('checkServiceRequestAndSendToServer');
  /*SendPort mainSendPort = message['sendPort'];
  SendPort moorSendPort = message['moorSendPort'];

  MoorIsolate moorIsolate = MoorIsolate.fromConnectPort(moorSendPort);*/
  String token = map['token'];
  String BASEURL = map['apiUrlPath'];
  print('log path inside isolate ${map['logPath']}');
  Database db = await getWorkerDatabase(map);
  IsolateServiceResponse isolateServiceResponse = IsolateServiceResponse();
  //
  List<ServiceRequestData> pendingRequest =
      await db.serviceRequestDao.getPendingRequestFromDb();
  List<RepairData> pendingRepairList =
      await db.repairDao.getPendingRepairFromDb();
  if (pendingRequest != null && pendingRequest.isNotEmpty) {
    //Logger.i('SENDING PENDING SERVICE REQUEST');
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token
    };

    for (ServiceRequestData serviceRequestData in pendingRequest) {
      try {
        dynamic body = {
          'CURRENT_LOCATION_ID': serviceRequestData.locationId,
          'NEW_LOCATION_ID': serviceRequestData.newLocationId,
          'TASK_TYPE': serviceRequestData.requestChoiceType,
          // 'TASK_TYPE_DESC': serviceRequestData.requestChoiceType,
          'DESCRIPTION': serviceRequestData.description,
          'EXTN': serviceRequestData.extn != null
              ? json.decode(serviceRequestData.extn!)
              : null,
          'NEW_BARCODE': serviceRequestData.newBarcode,
          'REQUEST_TYPE': serviceRequestData.requestType,
          'PARTS': serviceRequestData.parts != null
              ? json.decode(serviceRequestData.parts!)
              : null,
          'DOCUMENT': serviceRequestData.document != null
              ? json.decode(serviceRequestData.document!)
              : null,
        };
        if (serviceRequestData.requestType == "NOTIFICATION") {
          body['AUDIT_ID'] = serviceRequestData.auditId;
        }
        if (serviceRequestData.requestType != 'EQUIPMENT') {
          if (serviceRequestData.equipmentId != null) {
            body['EQUIPMENT_ID'] = int.parse(serviceRequestData.equipmentId!);
          }
        } else {
          body['EQUIPMENT_NAME'] = serviceRequestData.equipmentName;
          body['EQUIPMENT_CATEGORY'] = serviceRequestData.equipmentCategory;
        }

        /// afs user service request
        if (serviceRequestData.requestType == "SERVICE_REQUEST" &&
            serviceRequestData.refId != null) {
          body['REF_ID'] = serviceRequestData.refId;
        }
        if (serviceRequestData.requestType == "SERVICE_REQUEST" &&
            serviceRequestData.safety != null &&
            serviceRequestData.safety != false) {
          body['SAFETY'] = 'Y';
        }
        if (serviceRequestData.latitude != null) {
          body['LATITUDE'] = serviceRequestData.latitude;
        }
        if (serviceRequestData.longitude != null) {
          body['LONGITUDE'] = serviceRequestData.longitude;
        }
        String query = "";
        if (serviceRequestData.requestType == "SERVICE_REQUEST" &&
            serviceRequestData.safety == true) {
          //Logger.i("======is safety issue======");
          query = "?SAFETY=Y";
        }
        isolateServiceResponse.apiUrl = "$BASEURL/servicerequest$query";
        final response = await http.post(
            Uri.parse("$BASEURL/servicerequest$query"),
            headers: requestHeaders,
            body: json.encode(body));
        //Logger.i('======SERVICE REQUEST ISOLATE===========');
        //Logger.i(json.encode(body));
        //Logger.i('http respond');
        //Logger.i('$BASEURL/servicerequest Conversation id: ${response.headers['conversation-id'].toString()}');
        //Logger.i(response.statusCode.toString());
        isolateServiceResponse.responseCode = response.statusCode;
        isolateServiceResponse.responseLength = response.contentLength ?? 0;
        isolateServiceResponse.conversationId = response.headers['conversation-id'].toString();
        if (response.statusCode == 204) {
          isolateServiceResponse.token = response.headers['authorization'];
          //Logger.i('http 204');
          isolateServiceResponse.sentServiceRequestCount++;
          if (serviceRequestData.requestType == 'SERVICE_REQUEST') {
            isolateServiceResponse.requestType = ServiceRequestType.ADD;
          } else if (serviceRequestData.requestType == 'NEW_BARCODE') {
            isolateServiceResponse.requestType =
                ServiceRequestType.REPLACE_BARCODE;
          } else if (serviceRequestData.requestType == 'MOVEMENT') {
            isolateServiceResponse.requestType =
                ServiceRequestType.MOVE_EQUIPMENT;
          } else if (serviceRequestData.requestType == 'EQUIPMENT') {
            isolateServiceResponse.requestType =
                ServiceRequestType.ASSIGN_EQUIPMENT;
          } else if (serviceRequestData.requestType == 'NOTIFICATION') {
            isolateServiceResponse.requestType =
                ServiceRequestType.NOTIFICATION;
          }
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          //Logger.i('======deleted service request record==========');
        } else if (response.statusCode == 401) {
          isolateServiceResponse.hasError = true;
          isolateServiceResponse.pendingServiceRequest++;
          isolateServiceResponse.error = "Invalid Authentication Credentials";
        } else if (response.statusCode == 500 || response.statusCode == 501) {
          print("delete the record from local db");
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          isolateServiceResponse.error = response.body;
          isolateServiceResponse.hasError = true;
          //isolateServiceResponse.pendingServiceRequest++;
        } else if (response.statusCode == 400) {
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          isolateServiceResponse.error = json.decode(response.body);
          isolateServiceResponse.hasError = true;
          //isolateServiceResponse.pendingServiceRequest++;
        } else if (response.statusCode == 503 || response.statusCode == 502) {
          print("delete the record from local db");
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          isolateServiceResponse.hasError = true;
          isolateServiceResponse.error =
              "Server is not responding, Status Code: " +
                  response.statusCode.toString();
          //isolateServiceResponse.pendingServiceRequest++;
          //Logger.e('======Unknown Error======\n' + response.body);
          //Logger.e('======Response Code======\n' + response.statusCode.toString());
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          /*log('======Unknown Error======\n' + response.body);
          log('======Response Code======\n' + response.statusCode.toString());*/
        } else {
          isolateServiceResponse.hasError = true;
          isolateServiceResponse.error = response.body;
          //isolateServiceResponse.pendingServiceRequest++;
          //Logger.e('======Unknown Error======\n' + response.body);
          //Logger.e('======Response Code======\n' + response.statusCode.toString());
          await db.serviceRequestDao.deleteServiceRequest(serviceRequestData);
          /*log('======Unknown Error======\n' + response.body);
          log('======Response Code======\n' + response.statusCode.toString());*/
        }
      } catch (e) {
        log(e.toString());
        //send pending request
        isolateServiceResponse.error = e.toString();
        isolateServiceResponse.hasError = true;
        isolateServiceResponse.pendingServiceRequest = pendingRequest.length;
        break;
      }
    }
    //mainSendPort.send(isolateServiceResponse);
    if (!kIsWeb) {
      getReturnServiceResponse(map, isolateServiceResponse);
    } else {
      return getReturnServiceResponse(map, isolateServiceResponse);
    }
  }

  if (pendingRepairList != null && pendingRepairList.isNotEmpty) {
    //Logger.i('SENDING PENDING REPAIR');
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token
    };

    for (RepairData repairData in pendingRepairList) {
      try {
        dynamic body = {
          'CURRENT_LOCATION': repairData.currentLocationId,
          'NEW_LOCATION': repairData.newLocationId,
          'EXTN':
              repairData.extn == null ? null : json.decode(repairData.extn!),
          'PARTS': json.decode(repairData.parts!),
          'DOCUMENT': json.decode(repairData.repairDocument!),
        };
        if (repairData.customerId != null) {
          body['CUSTOMER_ID'] = repairData.customerId;
        }
        if (repairData.requestId == null && repairData.refId == null) {
          body['SERVICE_REQUEST_DOCUMENT'] =
              json.decode(repairData.serviceRequestDocument!);
        } else {
          if (repairData.requestId != null) {
            body['REQUEST_ID'] = repairData.requestId;
          } else if (repairData.refId != null) {
            body['REF_ID'] = repairData.refId;
          }
        }
        if (repairData.latitude != null) {
          body['LATITUDE'] = repairData.latitude;
        }
        if (repairData.longitude != null) {
          body['LONGITUDE'] = repairData.longitude;
        }
        //Logger.i(json.encode(body));
        final response = await http.post(Uri.parse("$BASEURL/repair"),
            headers: requestHeaders, body: json.encode(body));
        //Logger.i("$BASEURL/repair Conversation id: ${response.headers['conversation-id'].toString()}");

        //Logger.i('http respond');
        if (response.statusCode == 204 || response.statusCode == 202) {
          isolateServiceResponse.token = response.headers['authorization'];
          //Logger.i('http 204');
          isolateServiceResponse.requestType = ServiceRequestType.REPAIR;
          await db.repairDao.deleteServiceRequest(repairData);
          isolateServiceResponse.sentRepairCount++;
          //Logger.i('deleted recorded');
        } else if (response.statusCode == 400) {
          await db.repairDao.deleteServiceRequest(repairData);
          isolateServiceResponse.error = json.decode(response.body);
          isolateServiceResponse.hasError = true;
        } else {
          isolateServiceResponse.hasError = true;
          isolateServiceResponse.pendingRepairCount++;
          isolateServiceResponse.error = response.body;
          //Logger.i('======HTTP Response======\n' + json.decode(response.body));
          //Logger.i('======Status Code======\n' + (response.statusCode).toString());
          await db.repairDao.deleteServiceRequest(repairData);
        }
      } catch (e) {
        //Logger.e('==============================REPAIR ERROR SERVER==============================');
        //Logger.e(e.toString());
        //send pending request
        isolateServiceResponse.hasError = true;
        isolateServiceResponse.pendingRepairCount = pendingRepairList.length;
        break;
      }
    }
    //mainSendPort.send(isolateServiceResponse);
    if (!kIsWeb) {
      getReturnServiceResponse(map, isolateServiceResponse);
    } else {
      return getReturnServiceResponse(map, isolateServiceResponse);
    }
  } else {
    //mainSendPort.send(isolateServiceResponse);

    if (!kIsWeb) {
      getReturnServiceResponse(map, isolateServiceResponse);
    } else {
      return getReturnServiceResponse(map, isolateServiceResponse);
    }
  }
}

class IsolateServiceResponse {
  String error = '';
  bool hasError = false;
  int sentServiceRequestCount = 0;
  int sentRepairCount = 0;
  int pendingServiceRequest = 0;
  int pendingRepairCount = 0;
  String? token;
  ServiceRequestType requestType = ServiceRequestType.ADD;
  String apiUrl = '';
  int responseCode = 0;
  int responseLength = 0;
  String conversationId="";
}
